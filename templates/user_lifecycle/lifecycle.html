{% extends 'base.html' %}
{% load static %}

{% block title %}{{ page_title }} - MMO游戏大R用户维护系统{% endblock %}

{% block breadcrumb %}{{ page_title }}{% endblock %}

{% block extra_css %}
<style>
/* 用户生命周期管理页面样式 */
.lifecycle-container {
    padding: var(--spacing-lg);
    max-width: 1600px;
    margin: 0 auto;
}

/* 页面头部 */
.page-header {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #8b5cf6, #06b6d4, #10b981);
}

.page-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: var(--spacing-xs);
    background: linear-gradient(135deg, #8b5cf6, #06b6d4);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.page-description {
    color: var(--muted-foreground);
    margin: 0;
    font-size: 1rem;
}

/* 用户搜索区域 */
.user-search-section {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.search-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.search-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.search-form {
    display: flex;
    gap: var(--spacing-md);
    align-items: center;
}

.search-input {
    flex: 1;
    min-width: 300px;
    padding: 12px 16px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--background);
    color: var(--foreground);
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-btn {
    padding: 12px 24px;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: var(--radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

/* 主要内容区域 */
.main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: var(--spacing-lg);
    align-items: start;
    gap: var(--spacing-lg);
}

/* 用户详情面板 */
.user-detail-panel {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    overflow: hidden;
    width: 400px;
    flex-shrink: 0;
}

.main-content:not(.has-user) .user-detail-panel {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}

.user-not-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    min-height: 400px;
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--muted);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    font-size: 2rem;
    color: var(--muted-foreground);
}

.empty-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: var(--spacing-sm);
}

.empty-description {
    color: var(--muted-foreground);
    font-size: 0.875rem;
}

/* 用户信息卡片 */
.user-info-card {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
}

.user-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary), var(--info));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.user-name {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.user-id {
    color: var(--muted-foreground);
    font-size: 0.875rem;
    margin-bottom: var(--spacing-md);
}

.user-tags {
    display: flex;
    gap: var(--spacing-xs);
    flex-wrap: wrap;
}

.user-tag {
    padding: 4px 8px;
    border-radius: var(--radius);
    font-size: 0.75rem;
    font-weight: 500;
}

.tag-vip {
    background: linear-gradient(135deg, #fbbf24, #f59e0b);
    color: white;
}

.tag-potential {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.tag-risk {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

/* 生命周期阶段 */
.lifecycle-stages {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border);
}

.stages-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: var(--spacing-md);
}

.stage-timeline {
    position: relative;
}

.stage-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) 0;
    position: relative;
}

.stage-item:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 15px;
    top: 40px;
    width: 2px;
    height: 20px;
    background: var(--border);
}

.stage-dot {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--muted);
    border: 3px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    font-size: 0.75rem;
    color: var(--muted-foreground);
    position: relative;
    z-index: 1;
}

.stage-dot.active {
    background: var(--primary);
    border-color: var(--primary);
    color: white;
}

.stage-dot.completed {
    background: var(--success);
    border-color: var(--success);
    color: white;
}

.stage-content {
    flex: 1;
}

.stage-name {
    font-weight: 500;
    color: var(--foreground);
    margin-bottom: 2px;
}

.stage-date {
    font-size: 0.75rem;
    color: var(--muted-foreground);
}

/* 快速操作 */
.quick-actions {
    padding: var(--spacing-lg);
}

.actions-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--foreground);
    margin-bottom: var(--spacing-md);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: 12px 16px;
    border: 1px solid var(--border);
    border-radius: var(--radius);
    background: var(--background);
    color: var(--foreground);
    text-decoration: none;
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.action-btn:hover {
    background: var(--muted);
    transform: translateY(-1px);
}

.action-btn.primary {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
}

.action-btn.primary:hover {
    background: var(--primary-dark);
}

/* 数据分析区域 */
.data-analysis-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
    flex: 1;
}

.main-content:not(.has-user) .data-analysis-area {
    display: none;
}

/* 统计卡片网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-md);
}

.stat-card {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin: 0 auto var(--spacing-md);
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: var(--foreground);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--muted-foreground);
}

/* 图表容器 */
.chart-container {
    background: var(--surface);
    border: 1px solid var(--border);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
}

.chart-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--foreground);
    margin: 0;
}

.chart-content {
    height: 300px;
    position: relative;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
    }

    .main-content:not(.has-user) .user-detail-panel {
        width: 100%;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .lifecycle-container {
        padding: var(--spacing-md);
    }
    
    .search-form {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-input {
        min-width: auto;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .user-tags {
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="lifecycle-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <h1 class="page-title">{{ page_title }}</h1>
        <p class="page-description">{{ page_description }}</p>
    </div>

    <!-- 用户搜索区域 -->
    <div class="user-search-section">
        <div class="search-header">
            <h2 class="search-title">用户搜索</h2>
        </div>
        <form class="search-form" id="userSearchForm">
            <input type="text" class="search-input" id="userSearchInput" 
                   placeholder="请输入用户ID、角色名或手机号进行搜索...">
            <button type="submit" class="search-btn">
                <i class="bi bi-search"></i>
                搜索用户
            </button>
        </form>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
        <!-- 数据分析区域 -->
        <div class="data-analysis-area">
            <!-- 统计卡片 -->
            <div class="stats-grid" id="userStatsGrid" style="display: none;">
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--primary);">
                        <i class="bi bi-calendar-check"></i>
                    </div>
                    <div class="stat-value" id="totalLoginDays">--</div>
                    <div class="stat-label">累计登录天数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--success);">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    <div class="stat-value" id="totalRecharge">--</div>
                    <div class="stat-label">累计充值金额</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--warning);">
                        <i class="bi bi-clock"></i>
                    </div>
                    <div class="stat-value" id="avgOnlineTime">--</div>
                    <div class="stat-label">平均在线时长</div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon" style="background: var(--info);">
                        <i class="bi bi-trophy"></i>
                    </div>
                    <div class="stat-value" id="vipLevel">--</div>
                    <div class="stat-label">当前VIP等级</div>
                </div>
            </div>

            <!-- 充值趋势图表 -->
            <div class="chart-container" id="rechargeChartContainer" style="display: none;">
                <div class="chart-header">
                    <h3 class="chart-title">充值趋势分析</h3>
                </div>
                <div class="chart-content">
                    <canvas id="rechargeChart"></canvas>
                </div>
            </div>

            <!-- 行为分析图表 -->
            <div class="chart-container" id="behaviorChartContainer" style="display: none;">
                <div class="chart-header">
                    <h3 class="chart-title">在线行为分析</h3>
                </div>
                <div class="chart-content">
                    <canvas id="behaviorChart"></canvas>
                </div>
            </div>
        </div>

        <!-- 用户详情面板 -->
        <div class="user-detail-panel">
            <!-- 未选择用户状态 -->
            <div class="user-not-selected" id="userNotSelected">
                <div class="empty-icon">
                    <i class="bi bi-person-circle"></i>
                </div>
                <h3 class="empty-title">请选择用户</h3>
                <p class="empty-description">在左侧搜索框中输入用户信息进行搜索</p>
            </div>

            <!-- 用户信息卡片 -->
            <div id="userInfoSection" style="display: none;">
                <div class="user-info-card">
                    <div class="user-avatar" id="userAvatar">U</div>
                    <h3 class="user-name" id="userName">用户名</h3>
                    <p class="user-id">ID: <span id="userId">123456</span></p>
                    <div class="user-tags" id="userTags">
                        <!-- 动态生成标签 -->
                    </div>
                </div>

                <!-- 生命周期阶段 -->
                <div class="lifecycle-stages">
                    <h4 class="stages-title">生命周期阶段</h4>
                    <div class="stage-timeline" id="stageTimeline">
                        <!-- 动态生成阶段 -->
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="quick-actions">
                    <h4 class="actions-title">快速操作</h4>
                    <div class="action-buttons">
                        <a href="#" class="action-btn primary" id="sendMessageBtn">
                            <i class="bi bi-chat-dots"></i>
                            发送定制消息
                        </a>
                        <a href="#" class="action-btn" id="sendGiftBtn">
                            <i class="bi bi-gift"></i>
                            赠送专属礼包
                        </a>
                        <a href="#" class="action-btn" id="assignServiceBtn">
                            <i class="bi bi-headset"></i>
                            安排专属客服
                        </a>
                        <a href="#" class="action-btn" id="exportReportBtn">
                            <i class="bi bi-file-earmark-text"></i>
                            导出用户报告
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 用户生命周期管理页面JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 用户生命周期管理页面加载');

    let rechargeChart = null;
    let behaviorChart = null;
    let currentUser = null;

    // 初始化页面
    initializePage();

    function initializePage() {
        // 绑定搜索表单事件
        bindSearchForm();

        // 绑定快速操作按钮
        bindActionButtons();

        console.log('✅ 页面初始化完成');
    }

    // 绑定搜索表单事件
    function bindSearchForm() {
        const searchForm = document.getElementById('userSearchForm');
        const searchInput = document.getElementById('userSearchInput');

        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchTerm = searchInput.value.trim();
            if (searchTerm) {
                searchUser(searchTerm);
            }
        });

        // 支持回车搜索
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                searchForm.dispatchEvent(new Event('submit'));
            }
        });
    }

    // 搜索用户
    async function searchUser(searchTerm) {
        try {
            console.log('🔍 搜索用户:', searchTerm);

            // 显示加载状态
            showLoadingState();

            // 模拟API调用 - 后续替换为真实API
            const userData = await mockSearchUser(searchTerm);

            if (userData) {
                currentUser = userData;
                displayUserInfo(userData);
                loadUserAnalytics(userData);
            } else {
                showUserNotFound();
            }

        } catch (error) {
            console.error('❌ 搜索用户失败:', error);
            showError('搜索失败，请稍后重试');
        }
    }

    // 模拟用户搜索API
    async function mockSearchUser(searchTerm) {
        // 模拟网络延迟
        await new Promise(resolve => setTimeout(resolve, 800));

        // 模拟用户数据
        const mockUsers = [
            {
                id: 123456,
                name: '龙战天下001',
                avatar: '龙',
                vipLevel: 8,
                totalRecharge: 25680,
                totalLoginDays: 156,
                avgOnlineTime: 4.5,
                isPotential: true,
                isChurnRisk: false,
                registrationDate: '2024-03-15',
                lastLoginDate: '2025-08-05',
                lifecycleStage: 'mature',
                rechargeHistory: [
                    { date: '2025-07-01', amount: 500 },
                    { date: '2025-07-15', amount: 1200 },
                    { date: '2025-08-01', amount: 800 },
                    { date: '2025-08-05', amount: 300 }
                ],
                behaviorData: [
                    { date: '2025-08-01', onlineTime: 5.2, pvpCount: 8 },
                    { date: '2025-08-02', onlineTime: 3.8, pvpCount: 5 },
                    { date: '2025-08-03', onlineTime: 6.1, pvpCount: 12 },
                    { date: '2025-08-04', onlineTime: 4.3, pvpCount: 7 },
                    { date: '2025-08-05', onlineTime: 5.7, pvpCount: 9 }
                ]
            },
            {
                id: 789012,
                name: '测试用户002',
                avatar: '测',
                vipLevel: 5,
                totalRecharge: 8900,
                totalLoginDays: 89,
                avgOnlineTime: 2.8,
                isPotential: false,
                isChurnRisk: true,
                registrationDate: '2024-06-20',
                lastLoginDate: '2025-07-28',
                lifecycleStage: 'decline',
                rechargeHistory: [
                    { date: '2025-06-01', amount: 200 },
                    { date: '2025-06-20', amount: 500 },
                    { date: '2025-07-10', amount: 100 }
                ],
                behaviorData: [
                    { date: '2025-07-24', onlineTime: 1.2, pvpCount: 1 },
                    { date: '2025-07-26', onlineTime: 0.8, pvpCount: 0 },
                    { date: '2025-07-28', onlineTime: 2.1, pvpCount: 2 }
                ]
            }
        ];

        // 根据搜索词匹配用户
        const user = mockUsers.find(u =>
            u.id.toString().includes(searchTerm) ||
            u.name.toLowerCase().includes(searchTerm.toLowerCase())
        );

        return user || null;
    }

    // 显示加载状态
    function showLoadingState() {
        const notSelectedDiv = document.getElementById('userNotSelected');
        notSelectedDiv.innerHTML = `
            <div class="empty-icon">
                <div class="loading-spinner"></div>
            </div>
            <h3 class="empty-title">搜索中...</h3>
            <p class="empty-description">正在查找用户信息</p>
        `;

        // 添加加载动画样式
        if (!document.querySelector('.loading-spinner-style')) {
            const style = document.createElement('style');
            style.className = 'loading-spinner-style';
            style.textContent = `
                .loading-spinner {
                    width: 32px;
                    height: 32px;
                    border: 3px solid var(--border);
                    border-top: 3px solid var(--primary);
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            document.head.appendChild(style);
        }
    }

    // 显示用户未找到
    function showUserNotFound() {
        // 移除has-user类
        document.querySelector('.main-content').classList.remove('has-user');

        const notSelectedDiv = document.getElementById('userNotSelected');
        notSelectedDiv.style.display = 'flex';
        notSelectedDiv.innerHTML = `
            <div class="empty-icon">
                <i class="bi bi-person-x"></i>
            </div>
            <h3 class="empty-title">未找到用户</h3>
            <p class="empty-description">请检查搜索条件后重试</p>
        `;

        // 隐藏用户信息和分析区域
        document.getElementById('userInfoSection').style.display = 'none';
        document.getElementById('userStatsGrid').style.display = 'none';
        document.getElementById('rechargeChartContainer').style.display = 'none';
        document.getElementById('behaviorChartContainer').style.display = 'none';
    }

    // 显示错误信息
    function showError(message) {
        // 移除has-user类
        document.querySelector('.main-content').classList.remove('has-user');

        const notSelectedDiv = document.getElementById('userNotSelected');
        notSelectedDiv.style.display = 'flex';
        notSelectedDiv.innerHTML = `
            <div class="empty-icon" style="background: var(--error);">
                <i class="bi bi-exclamation-triangle"></i>
            </div>
            <h3 class="empty-title">搜索失败</h3>
            <p class="empty-description">${message}</p>
        `;

        // 隐藏用户信息和分析区域
        document.getElementById('userInfoSection').style.display = 'none';
        document.getElementById('userStatsGrid').style.display = 'none';
        document.getElementById('rechargeChartContainer').style.display = 'none';
        document.getElementById('behaviorChartContainer').style.display = 'none';
    }

    // 显示用户信息
    function displayUserInfo(user) {
        // 添加has-user类到主内容区域
        document.querySelector('.main-content').classList.add('has-user');

        // 隐藏未选择状态
        document.getElementById('userNotSelected').style.display = 'none';

        // 显示用户信息区域
        document.getElementById('userInfoSection').style.display = 'block';

        // 更新用户基本信息
        document.getElementById('userAvatar').textContent = user.avatar;
        document.getElementById('userName').textContent = user.name;
        document.getElementById('userId').textContent = user.id;

        // 更新用户标签
        updateUserTags(user);

        // 更新生命周期阶段
        updateLifecycleStages(user);

        console.log('✅ 用户信息显示完成');
    }

    // 更新用户标签
    function updateUserTags(user) {
        const tagsContainer = document.getElementById('userTags');
        tagsContainer.innerHTML = '';

        // VIP等级标签
        const vipTag = document.createElement('span');
        vipTag.className = 'user-tag tag-vip';
        vipTag.textContent = `VIP${user.vipLevel}`;
        tagsContainer.appendChild(vipTag);

        // 潜力用户标签
        if (user.isPotential) {
            const potentialTag = document.createElement('span');
            potentialTag.className = 'user-tag tag-potential';
            potentialTag.textContent = '潜力用户';
            tagsContainer.appendChild(potentialTag);
        }

        // 流失风险标签
        if (user.isChurnRisk) {
            const riskTag = document.createElement('span');
            riskTag.className = 'user-tag tag-risk';
            riskTag.textContent = '流失风险';
            tagsContainer.appendChild(riskTag);
        }
    }

    // 更新生命周期阶段
    function updateLifecycleStages(user) {
        const stageTimeline = document.getElementById('stageTimeline');

        // 定义生命周期阶段
        const stages = [
            { name: '新用户', key: 'new', date: user.registrationDate },
            { name: '成长期', key: 'growth', date: '2024-04-15' },
            { name: '成熟期', key: 'mature', date: '2024-08-20' },
            { name: '衰退期', key: 'decline', date: null },
            { name: '流失期', key: 'churn', date: null }
        ];

        stageTimeline.innerHTML = '';

        stages.forEach((stage, index) => {
            const stageItem = document.createElement('div');
            stageItem.className = 'stage-item';

            const stageDot = document.createElement('div');
            stageDot.className = 'stage-dot';

            // 根据用户当前阶段设置状态
            if (stage.key === user.lifecycleStage) {
                stageDot.classList.add('active');
                stageDot.innerHTML = '<i class="bi bi-circle-fill"></i>';
            } else if (index < getStageIndex(user.lifecycleStage)) {
                stageDot.classList.add('completed');
                stageDot.innerHTML = '<i class="bi bi-check"></i>';
            } else {
                stageDot.innerHTML = '<i class="bi bi-circle"></i>';
            }

            const stageContent = document.createElement('div');
            stageContent.className = 'stage-content';

            const stageName = document.createElement('div');
            stageName.className = 'stage-name';
            stageName.textContent = stage.name;

            const stageDate = document.createElement('div');
            stageDate.className = 'stage-date';
            stageDate.textContent = stage.date ? formatDate(stage.date) : '未达到';

            stageContent.appendChild(stageName);
            stageContent.appendChild(stageDate);

            stageItem.appendChild(stageDot);
            stageItem.appendChild(stageContent);

            stageTimeline.appendChild(stageItem);
        });
    }

    // 获取阶段索引
    function getStageIndex(stageKey) {
        const stageOrder = ['new', 'growth', 'mature', 'decline', 'churn'];
        return stageOrder.indexOf(stageKey);
    }

    // 格式化日期
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // 加载用户分析数据
    function loadUserAnalytics(user) {
        // 显示统计卡片
        document.getElementById('userStatsGrid').style.display = 'grid';

        // 更新统计数据
        updateUserStats(user);

        // 显示并初始化图表
        document.getElementById('rechargeChartContainer').style.display = 'block';
        document.getElementById('behaviorChartContainer').style.display = 'block';

        initializeRechargeChart(user.rechargeHistory);
        initializeBehaviorChart(user.behaviorData);

        console.log('✅ 用户分析数据加载完成');
    }

    // 更新用户统计数据
    function updateUserStats(user) {
        // 累计登录天数
        animateValue('totalLoginDays', 0, user.totalLoginDays, 1000);

        // 累计充值金额
        document.getElementById('totalRecharge').textContent = `¥${user.totalRecharge.toLocaleString()}`;

        // 平均在线时长
        document.getElementById('avgOnlineTime').textContent = `${user.avgOnlineTime}小时`;

        // VIP等级
        document.getElementById('vipLevel').textContent = `VIP${user.vipLevel}`;
    }

    // 数值动画
    function animateValue(elementId, start, end, duration) {
        const element = document.getElementById(elementId);
        const range = end - start;
        const increment = range / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= end) {
                current = end;
                clearInterval(timer);
            }
            element.textContent = Math.floor(current).toLocaleString();
        }, 16);
    }

    // 初始化充值趋势图表
    function initializeRechargeChart(rechargeHistory) {
        const ctx = document.getElementById('rechargeChart').getContext('2d');

        // 销毁现有图表
        if (rechargeChart) {
            rechargeChart.destroy();
        }

        const labels = rechargeHistory.map(item => formatDate(item.date));
        const data = rechargeHistory.map(item => item.amount);

        rechargeChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '充值金额',
                    data: data,
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#10b981',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(107, 114, 128, 0.1)'
                        },
                        ticks: {
                            color: '#6b7280',
                            callback: function(value) {
                                return '¥' + value.toLocaleString();
                            }
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    // 初始化行为分析图表
    function initializeBehaviorChart(behaviorData) {
        const ctx = document.getElementById('behaviorChart').getContext('2d');

        // 销毁现有图表
        if (behaviorChart) {
            behaviorChart.destroy();
        }

        const labels = behaviorData.map(item => formatDate(item.date));
        const onlineTimeData = behaviorData.map(item => item.onlineTime);
        const pvpCountData = behaviorData.map(item => item.pvpCount);

        behaviorChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '在线时长(小时)',
                    data: onlineTimeData,
                    backgroundColor: 'rgba(59, 130, 246, 0.8)',
                    borderColor: '#3b82f6',
                    borderWidth: 1,
                    yAxisID: 'y'
                }, {
                    label: 'PVP次数',
                    data: pvpCountData,
                    backgroundColor: 'rgba(245, 158, 11, 0.8)',
                    borderColor: '#f59e0b',
                    borderWidth: 1,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top'
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        grid: {
                            color: 'rgba(107, 114, 128, 0.1)'
                        },
                        ticks: {
                            color: '#6b7280'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        grid: {
                            drawOnChartArea: false
                        },
                        ticks: {
                            color: '#6b7280'
                        }
                    }
                }
            }
        });
    }

    // 绑定快速操作按钮
    function bindActionButtons() {
        // 发送定制消息
        document.getElementById('sendMessageBtn').addEventListener('click', function(e) {
            e.preventDefault();
            if (currentUser) {
                showActionModal('发送定制消息', `为用户 ${currentUser.name} 发送个性化消息`);
            }
        });

        // 赠送专属礼包
        document.getElementById('sendGiftBtn').addEventListener('click', function(e) {
            e.preventDefault();
            if (currentUser) {
                showActionModal('赠送专属礼包', `为用户 ${currentUser.name} 赠送VIP${currentUser.vipLevel}专属礼包`);
            }
        });

        // 安排专属客服
        document.getElementById('assignServiceBtn').addEventListener('click', function(e) {
            e.preventDefault();
            if (currentUser) {
                showActionModal('安排专属客服', `为用户 ${currentUser.name} 安排专属客服服务`);
            }
        });

        // 导出用户报告
        document.getElementById('exportReportBtn').addEventListener('click', function(e) {
            e.preventDefault();
            if (currentUser) {
                exportUserReport(currentUser);
            }
        });
    }

    // 显示操作模态框
    function showActionModal(title, message) {
        alert(`${title}\n\n${message}\n\n此功能将在后续版本中实现。`);
    }

    // 导出用户报告
    function exportUserReport(user) {
        console.log('📊 导出用户报告:', user.name);
        alert(`正在导出用户 ${user.name} 的详细报告...\n\n此功能将在后续版本中实现。`);
    }
});
</script>
{% endblock %}
